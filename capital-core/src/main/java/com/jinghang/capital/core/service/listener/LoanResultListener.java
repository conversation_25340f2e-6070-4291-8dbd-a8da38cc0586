package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.LockService;
import com.jinghang.capital.core.service.Locker;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class LoanResultListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanResultListener.class);

    private final int five = 5;
    private final int waitTime = 2;

    public LoanResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @Autowired
    private LockService lockService;

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_QUERY)
    public void listenLoanResult(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("监听放款结果:{}", loanId);

        Locker lock = lockService.getLock("fin_loanId_query_" + loanId);

        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
            if (locked) {
                // manageService
                manageService.loanQuery(loanId);
            }
        } catch (Exception e) {
            logger.error("查询放款结果异常", e);
            processException(loanId, message, e, "查询放款结果异常", getMqService()::submitLoanResultQueryDelay);
        } finally {
            lock.unlock();
            ackMsg(loanId, message, channel);
        }
    }
}
