package com.jinghang.capital.core.service.loan;

import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2025-07-11 16:09
 */
@ActiveProfiles("test")
@SpringBootTest
public class LoanServiceTest {
    @Autowired
    private ManageService manageService;


    @Test
    public void testStartLoan(){
        LoanApplyVo loanApplyVo = LoanApplyVoBuilder.defaultTestVo();
        manageService.loanApply(loanApplyVo);
    }
}

